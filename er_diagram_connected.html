<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>整齐ER图 - 精确连线版本</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            width: 1200px;
            height: 800px;
            background: white;
            border: 1px solid #ccc;
            position: relative;
            overflow: auto;
            margin: 0 auto;
        }
        .entity {
            position: absolute;
            background: white;
            border: 2px solid #000;
            padding: 10px 20px;
            font-weight: bold;
            text-align: center;
            min-width: 80px;
            z-index: 10;
        }
        .attribute {
            position: absolute;
            background: white;
            border: 1px solid #000;
            border-radius: 50%;
            padding: 6px 10px;
            font-size: 12px;
            text-align: center;
            min-width: 40px;
            z-index: 10;
        }
        .relationship {
            position: absolute;
            background: white;
            border: 2px solid #000;
            padding: 8px 16px;
            font-weight: bold;
            text-align: center;
            transform: rotate(45deg);
            min-width: 50px;
            z-index: 10;
        }
        .cardinality {
            position: absolute;
            font-weight: bold;
            font-size: 16px;
            z-index: 15;
        }
        svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        .connection-line {
            stroke: #000;
            stroke-width: 2;
            fill: none;
        }
    </style>
</head>
<body>
    <h1>整齐ER图 - 精确连线版本</h1>
    <div class="container">
        <svg>
            <!-- 第一行：用户 -> 课程 -> 教师 -->

            <!-- 用户实体的属性连接线 -->
            <line class="connection-line" x1="80" y1="40" x2="120" y2="80" />
            <line class="connection-line" x1="160" y1="40" x2="120" y2="80" />
            <line class="connection-line" x1="240" y1="40" x2="120" y2="80" />
            <line class="connection-line" x1="40" y1="120" x2="80" y2="100" />
            <line class="connection-line" x1="40" y1="160" x2="80" y2="120" />
            <line class="connection-line" x1="160" y1="160" x2="120" y2="140" />
            <line class="connection-line" x1="240" y1="160" x2="160" y2="140" />
            <line class="connection-line" x1="80" y1="200" x2="100" y2="160" />
            <line class="connection-line" x1="80" y1="240" x2="100" y2="180" />

            <!-- 用户到选择关系 -->
            <line class="connection-line" x1="180" y1="120" x2="240" y2="120" />

            <!-- 选择关系到课程 -->
            <line class="connection-line" x1="300" y1="120" x2="360" y2="120" />

            <!-- 课程实体的属性连接线 -->
            <line class="connection-line" x1="400" y1="40" x2="400" y2="80" />
            <line class="connection-line" x1="480" y1="40" x2="420" y2="80" />
            <line class="connection-line" x1="360" y1="160" x2="380" y2="140" />
            <line class="connection-line" x1="440" y1="160" x2="420" y2="140" />
            <line class="connection-line" x1="520" y1="160" x2="460" y2="140" />
            <line class="connection-line" x1="400" y1="200" x2="400" y2="160" />
            <line class="connection-line" x1="480" y1="200" x2="440" y2="160" />

            <!-- 课程到学习关系 -->
            <line class="connection-line" x1="500" y1="120" x2="560" y2="120" />

            <!-- 学习关系到教师 -->
            <line class="connection-line" x1="620" y1="120" x2="680" y2="120" />

            <!-- 教师实体的属性连接线 -->
            <line class="connection-line" x1="720" y1="40" x2="720" y2="80" />
            <line class="connection-line" x1="800" y1="40" x2="740" y2="80" />
            <line class="connection-line" x1="880" y1="40" x2="760" y2="80" />
            <line class="connection-line" x1="680" y1="160" x2="700" y2="140" />
            <line class="connection-line" x1="760" y1="160" x2="720" y2="140" />
            <line class="connection-line" x1="840" y1="160" x2="760" y2="140" />
            <line class="connection-line" x1="720" y1="200" x2="720" y2="160" />
            <line class="connection-line" x1="800" y1="200" x2="750" y2="160" />

            <!-- 第二行：试验 -> 教师 -->

            <!-- 试验实体的属性连接线 -->
            <line class="connection-line" x1="80" y1="280" x2="120" y2="320" />
            <line class="connection-line" x1="160" y1="280" x2="140" y2="320" />
            <line class="connection-line" x1="40" y1="360" x2="100" y2="340" />
            <line class="connection-line" x1="120" y1="360" x2="120" y2="340" />
            <line class="connection-line" x1="200" y1="360" x2="140" y2="340" />

            <!-- 试验到指导关系 -->
            <line class="connection-line" x1="180" y1="330" x2="240" y2="330" />

            <!-- 指导关系到教师（连接到上方教师） -->
            <line class="connection-line" x1="300" y1="330" x2="700" y2="160" />

            <!-- 第三行：教师 -> 点播课程 -> 成绩 -->

            <!-- 教师（下方）实体的属性连接线 -->
            <line class="connection-line" x1="320" y1="400" x2="320" y2="440" />
            <line class="connection-line" x1="280" y1="480" x2="300" y2="460" />
            <line class="connection-line" x1="360" y1="480" x2="340" y2="460" />
            <line class="connection-line" x1="320" y1="520" x2="320" y2="480" />
            <line class="connection-line" x1="400" y1="520" x2="350" y2="480" />

            <!-- 教师到教学关系 -->
            <line class="connection-line" x1="380" y1="450" x2="440" y2="450" />

            <!-- 教学关系到点播课程 -->
            <line class="connection-line" x1="500" y1="450" x2="560" y2="450" />

            <!-- 点播课程实体的属性连接线 -->
            <line class="connection-line" x1="600" y1="400" x2="600" y2="440" />
            <line class="connection-line" x1="560" y1="480" x2="580" y2="460" />
            <line class="connection-line" x1="640" y1="480" x2="620" y2="460" />

            <!-- 点播课程到属于关系 -->
            <line class="connection-line" x1="660" y1="450" x2="720" y2="450" />

            <!-- 属于关系到成绩 -->
            <line class="connection-line" x1="780" y1="450" x2="840" y2="450" />

            <!-- 成绩实体的属性连接线 -->
            <line class="connection-line" x1="880" y1="400" x2="880" y2="440" />
            <line class="connection-line" x1="840" y1="480" x2="860" y2="460" />
            <line class="connection-line" x1="920" y1="480" x2="900" y2="460" />
            <line class="connection-line" x1="960" y1="480" x2="920" y2="460" />
            <line class="connection-line" x1="880" y1="520" x2="880" y2="480" />
            <line class="connection-line" x1="960" y1="520" x2="920" y2="490" />

            <!-- 第四行：网络课程 -> 前端课程 -->

            <!-- 网络课程实体的属性连接线 -->
            <line class="connection-line" x1="320" y1="560" x2="320" y2="600" />
            <line class="connection-line" x1="280" y1="640" x2="300" y2="620" />
            <line class="connection-line" x1="360" y1="640" x2="340" y2="620" />

            <!-- 网络课程到属于关系 -->
            <line class="connection-line" x1="380" y1="610" x2="440" y2="610" />

            <!-- 属于关系到前端课程 -->
            <line class="connection-line" x1="500" y1="610" x2="560" y2="610" />

            <!-- 前端课程实体的属性连接线 -->
            <line class="connection-line" x1="600" y1="560" x2="600" y2="600" />
            <line class="connection-line" x1="560" y1="640" x2="580" y2="620" />
            <line class="connection-line" x1="640" y1="640" x2="620" y2="620" />
            <line class="connection-line" x1="720" y1="640" x2="650" y2="620" />

            <!-- 教师到教学关系的连接（从上方教师到下方教学） -->
            <line class="connection-line" x1="720" y1="160" x2="470" y2="420" />
        </svg>

        <!-- 用户实体 -->
        <div class="entity" style="left: 100px; top: 120px;">用户</div>
        
        <!-- 用户属性 -->
        <div class="attribute" style="left: 50px; top: 50px;">ID</div>
        <div class="attribute" style="left: 150px; top: 50px;">角色</div>
        <div class="attribute" style="left: 250px; top: 50px;">ID</div>
        <div class="attribute" style="left: 20px; top: 150px;">密码</div>
        <div class="attribute" style="left: 20px; top: 200px;">姓名</div>
        <div class="attribute" style="left: 150px; top: 200px;">性别</div>
        <div class="attribute" style="left: 250px; top: 200px;">电话</div>
        <div class="attribute" style="left: 20px; top: 250px;">专业</div>
        <div class="attribute" style="left: 50px; top: 300px;">ID</div>
        
        <!-- 选择关系 -->
        <div class="relationship" style="left: 280px; top: 120px;">选择</div>
        <div class="cardinality" style="left: 250px; top: 130px;">1</div>
        <div class="cardinality" style="left: 380px; top: 130px;">N</div>
        
        <!-- 课程实体 -->
        <div class="entity" style="left: 450px; top: 120px;">课程</div>
        
        <!-- 课程属性 -->
        <div class="attribute" style="left: 450px; top: 50px;">ID</div>
        <div class="attribute" style="left: 550px; top: 50px;">课程名</div>
        <div class="attribute" style="left: 420px; top: 200px;">学分</div>
        <div class="attribute" style="left: 520px; top: 200px;">价格</div>
        <div class="attribute" style="left: 620px; top: 200px;">课时</div>
        <div class="attribute" style="left: 450px; top: 250px;">价格</div>
        <div class="attribute" style="left: 550px; top: 250px;">自编号</div>
        
        <!-- 学习关系 -->
        <div class="relationship" style="left: 640px; top: 120px;">学习</div>
        <div class="cardinality" style="left: 610px; top: 130px;">1</div>
        <div class="cardinality" style="left: 740px; top: 130px;">N</div>
        
        <!-- 教师实体 -->
        <div class="entity" style="left: 800px; top: 120px;">教师</div>
        
        <!-- 教师属性 -->
        <div class="attribute" style="left: 850px; top: 50px;">ID</div>
        <div class="attribute" style="left: 950px; top: 50px;">所属单位ID</div>
        <div class="attribute" style="left: 1050px; top: 50px;">所属课程ID</div>
        <div class="attribute" style="left: 820px; top: 200px;">姓名</div>
        <div class="attribute" style="left: 920px; top: 200px;">修改时间</div>
        <div class="attribute" style="left: 1020px; top: 200px;">模块</div>
        <div class="attribute" style="left: 850px; top: 250px;">性别</div>
        <div class="attribute" style="left: 950px; top: 250px;">真实姓名</div>
        
        <!-- 试验实体 -->
        <div class="entity" style="left: 120px; top: 400px;">试验</div>
        
        <!-- 试验属性 -->
        <div class="attribute" style="left: 70px; top: 350px;">ID</div>
        <div class="attribute" style="left: 170px; top: 350px;">分数</div>
        <div class="attribute" style="left: 20px; top: 450px;">试验ID</div>
        <div class="attribute" style="left: 120px; top: 450px;">开始时间</div>
        <div class="attribute" style="left: 220px; top: 450px;">所属</div>
        
        <!-- 指导关系 -->
        <div class="relationship" style="left: 280px; top: 395px;">指导</div>
        <div class="cardinality" style="left: 250px; top: 405px;">N</div>
        <div class="cardinality" style="left: 380px; top: 405px;">1</div>
        
        <!-- 教学关系 -->
        <div class="relationship" style="left: 540px; top: 330px;">教学</div>
        <div class="cardinality" style="left: 510px; top: 340px;">M</div>
        <div class="cardinality" style="left: 580px; top: 340px;">1</div>
        
        <!-- 教师实体（下方） -->
        <div class="entity" style="left: 370px; top: 500px;">教师</div>
        
        <!-- 教师（下方）属性 -->
        <div class="attribute" style="left: 370px; top: 450px;">ID</div>
        <div class="attribute" style="left: 320px; top: 550px;">用户ID</div>
        <div class="attribute" style="left: 420px; top: 550px;">判断分数</div>
        <div class="attribute" style="left: 370px; top: 600px;">高等分数</div>
        <div class="attribute" style="left: 470px; top: 600px;">多选分数</div>
        
        <!-- 点播课程实体 -->
        <div class="entity" style="left: 670px; top: 500px;">点播课程</div>
        
        <!-- 点播课程属性 -->
        <div class="attribute" style="left: 670px; top: 450px;">ID</div>
        <div class="attribute" style="left: 620px; top: 550px;">选项C</div>
        <div class="attribute" style="left: 720px; top: 550px;">选项B</div>
        
        <!-- 成绩实体 -->
        <div class="entity" style="left: 920px; top: 500px;">成绩</div>
        
        <!-- 成绩属性 -->
        <div class="attribute" style="left: 870px; top: 450px;">ID</div>
        <div class="attribute" style="left: 820px; top: 550px;">满意度ID</div>
        <div class="attribute" style="left: 920px; top: 550px;">试题分数</div>
        <div class="attribute" style="left: 970px; top: 550px;">类型</div>
        <div class="attribute" style="left: 870px; top: 600px;">题目ID</div>
        <div class="attribute" style="left: 970px; top: 600px;">试题分数</div>
        
        <!-- 网络课程实体 -->
        <div class="entity" style="left: 370px; top: 700px;">网络课程</div>
        
        <!-- 网络课程属性 -->
        <div class="attribute" style="left: 370px; top: 650px;">ID</div>
        <div class="attribute" style="left: 320px; top: 750px;">选项A</div>
        <div class="attribute" style="left: 420px; top: 750px;">选项B</div>
        
        <!-- 前端课程实体 -->
        <div class="entity" style="left: 720px; top: 700px;">前端课程</div>
        
        <!-- 前端课程属性 -->
        <div class="attribute" style="left: 670px; top: 650px;">ID</div>
        <div class="attribute" style="left: 620px; top: 750px;">试题内容</div>
        <div class="attribute" style="left: 720px; top: 750px;">选项</div>
        <div class="attribute" style="left: 770px; top: 750px;">答案解析</div>
        
        <!-- 关系菱形 -->
        <div class="relationship" style="left: 450px; top: 560px;">属于</div>
        <div class="cardinality" style="left: 420px; top: 570px;">M</div>
        <div class="cardinality" style="left: 520px; top: 570px;">N</div>
        
        <div class="relationship" style="left: 750px; top: 560px;">属于</div>
        <div class="cardinality" style="left: 720px; top: 570px;">M</div>
        <div class="cardinality" style="left: 820px; top: 570px;">N</div>
        
        <div class="relationship" style="left: 450px; top: 740px;">属于</div>
        <div class="cardinality" style="left: 420px; top: 750px;">M</div>
        <div class="cardinality" style="left: 520px; top: 750px;">N</div>
    </div>
</body>
</html>
