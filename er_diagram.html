<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教学管理系统 ER 图</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }

        .er-container {
            position: relative;
            width: 1400px;
            height: 900px;
            background-color: white;
            border: 1px solid #ccc;
            margin: 0 auto;
            overflow: hidden;
        }

        /* 实体样式 */
        .entity {
            position: absolute;
            background-color: white;
            border: 2px solid #000;
            padding: 8px 15px;
            text-align: center;
            font-weight: bold;
            font-size: 14px;
        }

        /* 属性样式 */
        .attribute {
            position: absolute;
            background-color: white;
            border: 2px solid #000;
            border-radius: 50%;
            padding: 5px 8px;
            text-align: center;
            font-size: 11px;
            white-space: nowrap;
        }

        /* 关系样式 */
        .relationship {
            position: absolute;
            background-color: white;
            border: 2px solid #000;
            padding: 8px;
            text-align: center;
            font-weight: bold;
            font-size: 12px;
            transform: rotate(45deg);
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .relationship span {
            transform: rotate(-45deg);
            white-space: nowrap;
        }

        /* 基数标记 */
        .cardinality {
            position: absolute;
            font-weight: bold;
            font-size: 16px;
            color: #000;
        }

        /* 主键属性 */
        .primary-key {
            text-decoration: underline;
            font-weight: bold;
        }

        /* 连接线使用SVG */
        .connection-line {
            position: absolute;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center;">教学管理系统 ER 图</h1>

    <div class="er-container">
        <!-- SVG for connection lines -->
        <svg width="1400" height="900" style="position: absolute; top: 0; left: 0; pointer-events: none;">
            <!-- 用户到选择关系的连线 -->
            <line x1="200" y1="130" x2="280" y2="130" stroke="black" stroke-width="2"/>
            <!-- 选择关系到课程的连线 -->
            <line x1="320" y1="130" x2="400" y2="130" stroke="black" stroke-width="2"/>
            <!-- 课程到学习关系的连线 -->
            <line x1="500" y1="130" x2="580" y2="130" stroke="black" stroke-width="2"/>
            <!-- 学习关系到教师的连线 -->
            <line x1="620" y1="130" x2="700" y2="130" stroke="black" stroke-width="2"/>
            <!-- 教师到授课关系的连线 -->
            <line x1="800" y1="130" x2="880" y2="130" stroke="black" stroke-width="2"/>
            <!-- 授课关系到课程的连线 -->
            <line x1="920" y1="130" x2="1000" y2="130" stroke="black" stroke-width="2"/>

            <!-- 教师到开课关系的连线 -->
            <line x1="200" y1="300" x2="280" y2="300" stroke="black" stroke-width="2"/>
            <!-- 开课关系到教学的连线 -->
            <line x1="320" y1="300" x2="400" y2="300" stroke="black" stroke-width="2"/>
            <!-- 教学到数字关系的连线 -->
            <line x1="500" y1="300" x2="580" y2="300" stroke="black" stroke-width="2"/>

            <!-- 点播课程相关连线 -->
            <line x1="800" y1="300" x2="880" y2="300" stroke="black" stroke-width="2"/>
            <line x1="920" y1="300" x2="1000" y2="300" stroke="black" stroke-width="2"/>

            <!-- 成绩相关连线 -->
            <line x1="800" y1="450" x2="880" y2="450" stroke="black" stroke-width="2"/>
            <line x1="920" y1="450" x2="1000" y2="450" stroke="black" stroke-width="2"/>

            <!-- 网络课程相关连线 -->
            <line x1="200" y1="600" x2="280" y2="600" stroke="black" stroke-width="2"/>
            <line x1="320" y1="600" x2="400" y2="600" stroke="black" stroke-width="2"/>

            <!-- 前端课程相关连线 -->
            <line x1="800" y1="600" x2="880" y2="600" stroke="black" stroke-width="2"/>
            <line x1="920" y1="600" x2="1000" y2="600" stroke="black" stroke-width="2"/>

            <!-- 垂直连线 -->
            <line x1="450" y1="180" x2="450" y2="250" stroke="black" stroke-width="2"/>
            <line x1="850" y1="180" x2="850" y2="250" stroke="black" stroke-width="2"/>
            <line x1="850" y1="350" x2="850" y2="400" stroke="black" stroke-width="2"/>
            <line x1="450" y1="350" x2="450" y2="550" stroke="black" stroke-width="2"/>
        </svg>

        <!-- 用户实体及其属性 -->
        <div class="entity" style="top: 110px; left: 150px;">用户</div>
        <div class="attribute primary-key" style="top: 60px; left: 120px;">ID</div>
        <div class="attribute" style="top: 60px; left: 180px;">角色</div>
        <div class="attribute" style="top: 80px; left: 80px;">姓名</div>
        <div class="attribute" style="top: 150px; left: 80px;">密码</div>
        <div class="attribute" style="top: 170px; left: 120px;">性别</div>

        <!-- 选择关系 -->
        <div class="relationship" style="top: 105px; left: 275px;">
            <span>选择</span>
        </div>
        <div class="cardinality" style="top: 110px; left: 250px;">1</div>
        <div class="cardinality" style="top: 110px; left: 350px;">N</div>

        <!-- 课程实体及其属性 -->
        <div class="entity" style="top: 110px; left: 400px;">课程</div>
        <div class="attribute primary-key" style="top: 60px; left: 380px;">ID</div>
        <div class="attribute" style="top: 60px; left: 440px;">课程名</div>
        <div class="attribute" style="top: 80px; left: 340px;">学分</div>
        <div class="attribute" style="top: 150px; left: 340px;">价格</div>
        <div class="attribute" style="top: 170px; left: 380px;">课时</div>

        <!-- 学习关系 -->
        <div class="relationship" style="top: 105px; left: 575px;">
            <span>学习</span>
        </div>
        <div class="cardinality" style="top: 110px; left: 550px;">1</div>
        <div class="cardinality" style="top: 110px; left: 650px;">N</div>

        <!-- 教师实体及其属性 -->
        <div class="entity" style="top: 110px; left: 700px;">教师</div>
        <div class="attribute primary-key" style="top: 60px; left: 680px;">ID</div>
        <div class="attribute" style="top: 60px; left: 740px;">用户编号</div>
        <div class="attribute" style="top: 80px; left: 640px;">用户等级ID</div>
        <div class="attribute" style="top: 150px; left: 640px;">用户等级</div>
        <div class="attribute" style="top: 170px; left: 680px;">描述</div>
        <div class="attribute" style="top: 170px; left: 740px;">描述等级</div>

        <!-- 授课关系 -->
        <div class="relationship" style="top: 105px; left: 875px;">
            <span>授课</span>
        </div>
        <div class="cardinality" style="top: 110px; left: 850px;">1</div>
        <div class="cardinality" style="top: 110px; left: 950px;">N</div>

        <!-- 课程实体（右侧） -->
        <div class="entity" style="top: 110px; left: 1000px;">课程</div>
        <div class="attribute primary-key" style="top: 60px; left: 980px;">ID</div>
        <div class="attribute" style="top: 60px; left: 1040px;">用户等级</div>
        <div class="attribute" style="top: 80px; left: 940px;">描述</div>
        <div class="attribute" style="top: 150px; left: 940px;">描述等级</div>

        <!-- 教师实体（左中） -->
        <div class="entity" style="top: 280px; left: 150px;">教师</div>
        <div class="attribute primary-key" style="top: 230px; left: 130px;">ID</div>
        <div class="attribute" style="top: 250px; left: 80px;">会员</div>
        <div class="attribute" style="top: 320px; left: 80px;">会员分类</div>

        <!-- 开课关系 -->
        <div class="relationship" style="top: 275px; left: 275px;">
            <span>开课</span>
        </div>
        <div class="cardinality" style="top: 280px; left: 250px;">N</div>
        <div class="cardinality" style="top: 280px; left: 350px;">M</div>

        <!-- 教学实体 -->
        <div class="entity" style="top: 280px; left: 400px;">教学</div>
        <div class="attribute primary-key" style="top: 230px; left: 380px;">ID</div>
        <div class="attribute" style="top: 250px; left: 340px;">课程</div>
        <div class="attribute" style="top: 320px; left: 340px;">课程</div>
        <div class="attribute" style="top: 340px; left: 380px;">课程</div>

        <!-- 数字关系 -->
        <div class="relationship" style="top: 275px; left: 575px;">
            <span>数字</span>
        </div>
        <div class="cardinality" style="top: 280px; left: 550px;">1</div>

        <!-- 点播课程实体 -->
        <div class="entity" style="top: 280px; left: 800px;">点播课程</div>
        <div class="attribute primary-key" style="top: 230px; left: 780px;">组织ID</div>
        <div class="attribute" style="top: 250px; left: 740px;">基础分类</div>
        <div class="attribute" style="top: 320px; left: 740px;">类型</div>
        <div class="attribute" style="top: 340px; left: 780px;">类型</div>
        <div class="attribute" style="top: 320px; left: 840px;">选课ID</div>
        <div class="attribute" style="top: 340px; left: 880px;">选课类型</div>

        <!-- 学习关系（右中） -->
        <div class="relationship" style="top: 275px; left: 975px;">
            <span>学习</span>
        </div>
        <div class="cardinality" style="top: 280px; left: 950px;">N</div>

        <!-- 成绩实体 -->
        <div class="entity" style="top: 430px; left: 800px;">成绩</div>
        <div class="attribute primary-key" style="top: 380px; left: 780px;">组织ID</div>
        <div class="attribute" style="top: 400px; left: 740px;">基础分类</div>
        <div class="attribute" style="top: 470px; left: 740px;">类型</div>
        <div class="attribute" style="top: 490px; left: 780px;">类型</div>
        <div class="attribute" style="top: 470px; left: 840px;">选课ID</div>
        <div class="attribute" style="top: 490px; left: 880px;">选课类型</div>

        <!-- 学习关系（右下） -->
        <div class="relationship" style="top: 425px; left: 975px;">
            <span>学习</span>
        </div>
        <div class="cardinality" style="top: 430px; left: 950px;">M</div>

        <!-- 课程实体（下方） -->
        <div class="entity" style="top: 550px; left: 400px;">课程</div>
        <div class="attribute" style="top: 500px; left: 380px;">学分</div>
        <div class="attribute" style="top: 520px; left: 340px;">学分</div>
        <div class="attribute" style="top: 590px; left: 340px;">学分</div>

        <!-- 学习关系（下方） -->
        <div class="relationship" style="top: 545px; left: 575px;">
            <span>学习</span>
        </div>
        <div class="cardinality" style="top: 550px; left: 550px;">M</div>

        <!-- 网络课程实体 -->
        <div class="entity" style="top: 580px; left: 150px;">网络课程</div>
        <div class="attribute primary-key" style="top: 530px; left: 130px;">课程ID</div>
        <div class="attribute" style="top: 550px; left: 80px;">课程分类</div>
        <div class="attribute" style="top: 620px; left: 80px;">课程</div>
        <div class="attribute" style="top: 640px; left: 130px;">选课</div>
        <div class="attribute" style="top: 620px; left: 200px;">选课人数</div>
        <div class="attribute" style="top: 640px; left: 240px;">选课类型</div>

        <!-- 学习关系（左下） -->
        <div class="relationship" style="top: 575px; left: 275px;">
            <span>学习</span>
        </div>
        <div class="cardinality" style="top: 580px; left: 250px;">N</div>
        <div class="cardinality" style="top: 580px; left: 350px;">N</div>

        <!-- 前端课程实体 -->
        <div class="entity" style="top: 580px; left: 1000px;">前端课程</div>
        <div class="attribute primary-key" style="top: 530px; left: 980px;">ID</div>
        <div class="attribute" style="top: 550px; left: 940px;">试题类型</div>
        <div class="attribute" style="top: 620px; left: 940px;">类型</div>
        <div class="attribute" style="top: 640px; left: 980px;">类型</div>

        <!-- 学习关系（右下） -->
        <div class="relationship" style="top: 575px; left: 875px;">
            <span>学习</span>
        </div>
        <div class="cardinality" style="top: 580px; left: 850px;">N</div>
        <div class="cardinality" style="top: 580px; left: 950px;">N</div>
    </div>

    <div style="margin-top: 20px; text-align: center; max-width: 1400px; margin-left: auto; margin-right: auto;">
        <h3>实体关系说明</h3>
        <div style="text-align: left; display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
            <div>
                <h4>主要实体：</h4>
                <ul>
                    <li><strong>用户</strong>：系统使用者，包含ID、角色、姓名、密码、性别等属性</li>
                    <li><strong>课程</strong>：教学内容单位，包含ID、课程名、学分、价格、课时等属性</li>
                    <li><strong>教师</strong>：教学人员，包含ID、用户编号、用户等级等属性</li>
                    <li><strong>教学</strong>：教学活动实体，连接教师和课程</li>
                    <li><strong>点播课程</strong>：特殊类型课程，包含组织ID、基础分类等属性</li>
                    <li><strong>成绩</strong>：学习评价结果，包含组织ID、基础分类、类型等属性</li>
                    <li><strong>网络课程</strong>：在线课程，包含课程ID、课程分类、选课人数等属性</li>
                    <li><strong>前端课程</strong>：前端技术课程，包含ID、试题类型等属性</li>
                </ul>
            </div>
            <div>
                <h4>主要关系：</h4>
                <ul>
                    <li><strong>选择</strong>：用户与课程之间的选课关系（1:N）</li>
                    <li><strong>学习</strong>：多个实体间的学习关系，包括：
                        <ul>
                            <li>课程与教师的学习关系（1:N）</li>
                            <li>点播课程的学习关系（N）</li>
                            <li>成绩的学习关系（M）</li>
                            <li>网络课程与课程的学习关系（N:N）</li>
                            <li>前端课程的学习关系（N:N）</li>
                        </ul>
                    </li>
                    <li><strong>开课</strong>：教师与教学之间的开课关系（N:M）</li>
                    <li><strong>授课</strong>：教师与课程之间的授课关系（1:N）</li>
                    <li><strong>数字</strong>：教学相关的数字化关系（1）</li>
                </ul>
            </div>
        </div>

        <div style="margin-top: 20px; padding: 15px; background-color: #f0f8ff; border-radius: 5px;">
            <h4>图例说明：</h4>
            <p>
                <span style="display: inline-block; width: 20px; height: 15px; border: 2px solid #000; margin-right: 5px;"></span>矩形：实体
                <span style="display: inline-block; width: 20px; height: 15px; border: 2px solid #000; border-radius: 50%; margin-left: 15px; margin-right: 5px;"></span>椭圆：属性
                <span style="display: inline-block; width: 15px; height: 15px; border: 2px solid #000; transform: rotate(45deg); margin-left: 15px; margin-right: 5px;"></span>菱形：关系
            </p>
            <p><u>下划线</u>：主键属性 | <strong>1, N, M</strong>：关系基数</p>
        </div>
    </div>
</body>
</html>
