<!--
  SmartInterview AI智能面试系统 - 翠绿色主题功能架构图（分层分区还原版）
  结构严格参考用户图片模板，竖向分层、分区色块、矩阵式功能块、SVG箭头，内容只保留项目真实功能。
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>SmartInterview 功能架构图（翠绿色分层分区）</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    :root {
      --green-main: #3ecf8e;
      --green-light: #e6f9f0;
      --green-dark: #1e9c5a;
      --green-mid: #a7f3d0;
      --green-bg1: #e6f9f0;
      --green-bg2: #d1fae5;
      --green-bg3: #b7eacb;
      --gray-bg: #f8fafc;
      --gray-border: #e5e7eb;
      --text-main: #1a3a2a;
      --text-sub: #388e5c;
      --block-radius: 12px;
    }
    body {
      background: var(--gray-bg);
      font-family: 'Segoe UI', 'Microsoft YaHei', Arial, sans-serif;
      margin: 0;
      padding: 0;
    }
    .archi-vertical-container {
      max-width: 900px;
      margin: 36px auto 36px auto;
      padding: 0 8px 0 8px;
    }
    .archi-title {
      text-align: center;
      font-size: 2.2rem;
      font-weight: 800;
      color: var(--green-dark);
      margin-bottom: 18px;
      letter-spacing: 2px;
      text-shadow: 0 2px 12px #3ecf8e33;
    }
    .archi-layer {
      border-radius: var(--block-radius);
      margin: 0 auto 0 auto;
      box-shadow: 0 2px 12px #3ecf8e22;
      padding: 18px 24px 12px 24px;
      position: relative;
      width: 100%;
      min-width: 0;
    }
    .archi-layer + .archi-arrow {
      margin: 0 auto;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 32px;
    }
    .archi-layer-title {
      font-size: 1.18rem;
      font-weight: 700;
      color: var(--green-dark);
      margin-bottom: 10px;
      letter-spacing: 1px;
      text-align: left;
    }
    .archi-role {
      background: var(--green-bg1);
      border: 2px solid var(--green-main);
      display: flex;
      justify-content: center;
      gap: 32px;
      padding: 16px 0 10px 0;
    }
    .archi-role-block {
      background: #fff;
      border: 2px solid var(--green-bg3);
      border-radius: var(--block-radius);
      padding: 10px 32px;
      font-size: 1.08rem;
      font-weight: 600;
      color: var(--text-main);
      display: flex;
      align-items: center;
      gap: 8px;
      box-shadow: 0 1.5px 8px #3ecf8e22;
    }
    .archi-ui {
      background: var(--green-bg2);
      border: 2px solid var(--green-main);
      display: flex;
      justify-content: center;
      gap: 24px;
      flex-wrap: wrap;
      padding: 16px 0 10px 0;
    }
    .archi-ui-block {
      background: #fff;
      border: 2px solid var(--green-bg3);
      border-radius: var(--block-radius);
      padding: 10px 18px;
      font-size: 1.05rem;
      font-weight: 500;
      color: var(--text-sub);
      display: flex;
      align-items: center;
      gap: 8px;
      box-shadow: 0 1.5px 8px #3ecf8e22;
    }
    .archi-show {
      background: var(--green-bg1);
      border: 2px solid var(--green-main);
      display: flex;
      justify-content: center;
      gap: 18px;
      flex-wrap: wrap;
      padding: 16px 0 10px 0;
    }
    .archi-show-block {
      background: #fff;
      border: 2px solid var(--green-bg3);
      border-radius: var(--block-radius);
      padding: 10px 16px;
      font-size: 1.05rem;
      font-weight: 500;
      color: var(--text-sub);
      display: flex;
      align-items: center;
      gap: 8px;
      box-shadow: 0 1.5px 8px #3ecf8e22;
    }
    .archi-business {
      background: var(--green-bg2);
      border: 2px solid var(--green-main);
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 18px 24px;
      padding: 18px 0 10px 0;
    }
    .archi-business-group {
      background: var(--green-mid);
      border: 2px solid var(--green-bg3);
      border-radius: var(--block-radius);
      padding: 10px 12px 8px 12px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      box-shadow: 0 1.5px 8px #3ecf8e22;
    }
    .archi-business-group-title {
      font-size: 1.05rem;
      font-weight: 700;
      color: var(--green-dark);
      margin-bottom: 4px;
    }
    .archi-business-block {
      background: #fff;
      border: 2px solid var(--green-bg3);
      border-radius: 8px;
      padding: 7px 12px;
      font-size: 0.98rem;
      color: var(--text-sub);
      font-weight: 500;
      margin-bottom: 2px;
      display: flex;
      align-items: center;
      gap: 6px;
      box-shadow: 0 1px 4px #3ecf8e11;
    }
    .archi-support {
      background: var(--green-bg1);
      border: 2px solid var(--green-main);
      display: flex;
      justify-content: center;
      gap: 24px;
      flex-wrap: wrap;
      padding: 16px 0 10px 0;
    }
    .archi-support-block {
      background: #fff;
      border: 2px solid var(--green-bg3);
      border-radius: var(--block-radius);
      padding: 10px 18px;
      font-size: 1.05rem;
      font-weight: 500;
      color: var(--text-sub);
      display: flex;
      align-items: center;
      gap: 8px;
      box-shadow: 0 1.5px 8px #3ecf8e22;
    }
    .archi-db {
      background: var(--green-bg2);
      border: 2px solid var(--green-main);
      display: flex;
      justify-content: center;
      gap: 32px;
      padding: 16px 0 10px 0;
    }
    .archi-db-block {
      background: #fff;
      border: 2px solid var(--green-bg3);
      border-radius: var(--block-radius);
      padding: 10px 24px;
      font-size: 1.08rem;
      font-weight: 600;
      color: var(--green-dark);
      display: flex;
      align-items: center;
      gap: 8px;
      box-shadow: 0 1.5px 8px #3ecf8e22;
    }
    .archi-env {
      background: var(--green-bg1);
      border: 2px solid var(--green-main);
      display: flex;
      justify-content: center;
      gap: 32px;
      padding: 16px 0 10px 0;
    }
    .archi-env-block {
      background: #fff;
      border: 2px solid var(--green-bg3);
      border-radius: var(--block-radius);
      padding: 10px 24px;
      font-size: 1.08rem;
      font-weight: 600;
      color: var(--green-dark);
      display: flex;
      align-items: center;
      gap: 8px;
      box-shadow: 0 1.5px 8px #3ecf8e22;
    }
    @media (max-width: 700px) {
      .archi-business { grid-template-columns: 1fr; }
      .archi-db, .archi-env, .archi-role, .archi-ui, .archi-show, .archi-support { flex-direction: column; gap: 8px; }
    }
  </style>
</head>
<body>
<div class="archi-vertical-container">
  <div class="archi-title">SmartInterview AI智能面试系统<br>功能架构图</div>

  <!-- 角色层 -->
  <div class="archi-layer archi-role">
    <div class="archi-role-block"><i class="fa-solid fa-user"></i> 普通用户</div>
    <div class="archi-role-block"><i class="fa-solid fa-user-tie"></i> 管理员</div>
  </div>
  <div class="archi-arrow">
    <svg width="40" height="32"><polyline points="20,0 20,28" style="fill:none;stroke:#3ecf8e;stroke-width:3"/><polygon points="14,28 26,28 20,32" style="fill:#3ecf8e;"/></svg>
  </div>

  <!-- 前端UI层 -->
  <div class="archi-layer archi-ui">
    <div class="archi-ui-block"><i class="fa-brands fa-vuejs"></i> UniApp(Vue3)</div>
    <div class="archi-ui-block"><i class="fa-solid fa-mobile-screen"></i> H5/小程序/APP</div>
    <div class="archi-ui-block"><i class="fa-solid fa-palette"></i> TailwindCSS/UnoCSS</div>
    <div class="archi-ui-block"><i class="fa-solid fa-icons"></i> uni-icons</div>
  </div>
  <div class="archi-arrow">
    <svg width="40" height="32"><polyline points="20,0 20,28" style="fill:none;stroke:#3ecf8e;stroke-width:3"/><polygon points="14,28 26,28 20,32" style="fill:#3ecf8e;"/></svg>
  </div>

  <!-- 展示层 -->
  <div class="archi-layer archi-show">
    <div class="archi-show-block"><i class="fa-solid fa-comments"></i> AI聊天界面</div>
    <div class="archi-show-block"><i class="fa-solid fa-chalkboard-user"></i> 面试模块</div>
    <div class="archi-show-block"><i class="fa-solid fa-chart-line"></i> 技能评估</div>
    <div class="archi-show-block"><i class="fa-solid fa-book-open"></i> 学习中心</div>
    <div class="archi-show-block"><i class="fa-solid fa-user-circle"></i> 用户中心</div>
    <div class="archi-show-block"><i class="fa-solid fa-trophy"></i> 成就系统</div>
  </div>
  <div class="archi-arrow">
    <svg width="40" height="32"><polyline points="20,0 20,28" style="fill:none;stroke:#3ecf8e;stroke-width:3"/><polygon points="14,28 26,28 20,32" style="fill:#3ecf8e;"/></svg>
  </div>

  <!-- 业务层（AI Agent系统分组） -->
  <div class="archi-layer archi-business">
    <div class="archi-business-group">
      <div class="archi-business-group-title"><i class="fa-solid fa-robot"></i> AI Agent系统</div>
      <div class="archi-business-block">面试官AI Agent <span style="color:#1e9c5a;font-size:0.95em;">智能提问/追问</span></div>
      <div class="archi-business-block">简历分析AI Agent <span style="color:#1e9c5a;font-size:0.95em;">简历解析/技能匹配</span></div>
      <div class="archi-business-block">技能评估AI Agent <span style="color:#1e9c5a;font-size:0.95em;">技术测试/编程评估</span></div>
      <div class="archi-business-block">职业顾问AI Agent <span style="color:#1e9c5a;font-size:0.95em;">职业规划/行业分析</span></div>
      <div class="archi-business-block">模拟面试AI Agent <span style="color:#1e9c5a;font-size:0.95em;">真实面试模拟</span></div>
      <div class="archi-business-block">反馈分析AI Agent <span style="color:#1e9c5a;font-size:0.95em;">表现分析/多维评分</span></div>
      <div class="archi-business-block">学习指导AI Agent <span style="color:#1e9c5a;font-size:0.95em;">学习计划/资源推荐</span></div>
    </div>
    <div class="archi-business-group">
      <div class="archi-business-group-title"><i class="fa-solid fa-cubes"></i> 支撑服务/基础组件</div>
      <div class="archi-business-block">用户认证与权限管理</div>
      <div class="archi-business-block">AI Agent会话与对话管理</div>
      <div class="archi-business-block">技能评估与面试记录管理</div>
      <div class="archi-business-block">简历管理</div>
      <div class="archi-business-block">数据统计与分析</div>
    </div>
  </div>
  <div class="archi-arrow">
    <svg width="40" height="32"><polyline points="20,0 20,28" style="fill:none;stroke:#3ecf8e;stroke-width:3"/><polygon points="14,28 26,28 20,32" style="fill:#3ecf8e;"/></svg>
  </div>

  <!-- 数据层 -->
  <div class="archi-layer archi-db">
    <div class="archi-db-block"><i class="fa-solid fa-database"></i> MySQL数据库</div>
    <div class="archi-db-block"><i class="fa-solid fa-bolt"></i> Redis缓存</div>
  </div>
  <div class="archi-arrow">
    <svg width="40" height="32"><polyline points="20,0 20,28" style="fill:none;stroke:#3ecf8e;stroke-width:3"/><polygon points="14,28 26,28 20,32" style="fill:#3ecf8e;"/></svg>
  </div>

  <!-- 运行环境 -->
  <div class="archi-layer archi-env">
    <div class="archi-env-block"><i class="fa-brands fa-linux"></i> Linux服务器</div>
    <div class="archi-env-block"><i class="fa-solid fa-cloud"></i> 云主机/容器</div>
  </div>
</div>
</body>
</html> 