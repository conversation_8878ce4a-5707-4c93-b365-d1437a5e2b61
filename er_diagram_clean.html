<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>整齐ER图 - 精确连线版本</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            width: 1000px;
            height: 700px;
            background: white;
            border: 1px solid #ccc;
            position: relative;
            overflow: auto;
            margin: 0 auto;
        }
        .entity {
            position: absolute;
            background: white;
            border: 2px solid #000;
            padding: 10px 20px;
            font-weight: bold;
            text-align: center;
            min-width: 80px;
            z-index: 10;
        }
        .attribute {
            position: absolute;
            background: white;
            border: 1px solid #000;
            border-radius: 50%;
            padding: 6px 10px;
            font-size: 12px;
            text-align: center;
            min-width: 40px;
            z-index: 10;
        }
        .relationship {
            position: absolute;
            background: white;
            border: 2px solid #000;
            padding: 8px 16px;
            font-weight: bold;
            text-align: center;
            transform: rotate(45deg);
            min-width: 50px;
            z-index: 10;
        }
        .cardinality {
            position: absolute;
            font-weight: bold;
            font-size: 16px;
            z-index: 15;
        }
        svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        .connection-line {
            stroke: #000;
            stroke-width: 2;
            fill: none;
        }
    </style>
</head>
<body>
    <h1>整齐ER图 - 精确连线版本</h1>
    <div class="container">
        <svg>
            <!-- 第一行：用户 -> 课程 -> 教师 -->
            
            <!-- 用户实体的属性连接线 -->
            <line class="connection-line" x1="80" y1="30" x2="100" y2="60" />
            <line class="connection-line" x1="140" y1="30" x2="120" y2="60" />
            <line class="connection-line" x1="200" y1="30" x2="140" y2="60" />
            <line class="connection-line" x1="30" y1="90" x2="80" y2="80" />
            <line class="connection-line" x1="30" y1="120" x2="80" y2="100" />
            <line class="connection-line" x1="140" y1="120" x2="120" y2="100" />
            <line class="connection-line" x1="200" y1="120" x2="160" y2="100" />
            <line class="connection-line" x1="80" y1="150" x2="100" y2="120" />
            
            <!-- 用户到选择关系 -->
            <line class="connection-line" x1="180" y1="90" x2="220" y2="90" />
            
            <!-- 选择关系到课程 -->
            <line class="connection-line" x1="280" y1="90" x2="320" y2="90" />
            
            <!-- 课程实体的属性连接线 -->
            <line class="connection-line" x1="360" y1="30" x2="360" y2="60" />
            <line class="connection-line" x1="420" y1="30" x2="380" y2="60" />
            <line class="connection-line" x1="320" y1="120" x2="340" y2="100" />
            <line class="connection-line" x1="380" y1="120" x2="360" y2="100" />
            <line class="connection-line" x1="440" y1="120" x2="400" y2="100" />
            <line class="connection-line" x1="360" y1="150" x2="360" y2="120" />
            <line class="connection-line" x1="420" y1="150" x2="390" y2="120" />
            
            <!-- 课程到学习关系 -->
            <line class="connection-line" x1="440" y1="90" x2="480" y2="90" />
            
            <!-- 学习关系到教师 -->
            <line class="connection-line" x1="540" y1="90" x2="580" y2="90" />
            
            <!-- 教师实体的属性连接线 -->
            <line class="connection-line" x1="620" y1="30" x2="620" y2="60" />
            <line class="connection-line" x1="680" y1="30" x2="640" y2="60" />
            <line class="connection-line" x1="740" y1="30" x2="660" y2="60" />
            <line class="connection-line" x1="580" y1="120" x2="600" y2="100" />
            <line class="connection-line" x1="640" y1="120" x2="620" y2="100" />
            <line class="connection-line" x1="700" y1="120" x2="660" y2="100" />
            <line class="connection-line" x1="620" y1="150" x2="620" y2="120" />
            <line class="connection-line" x1="680" y1="150" x2="650" y2="120" />
            
            <!-- 第二行：试验 -> 教师 -->
            
            <!-- 试验实体的属性连接线 -->
            <line class="connection-line" x1="80" y1="200" x2="100" y2="230" />
            <line class="connection-line" x1="140" y1="200" x2="120" y2="230" />
            <line class="connection-line" x1="30" y1="260" x2="80" y2="250" />
            <line class="connection-line" x1="100" y1="260" x2="100" y2="250" />
            <line class="connection-line" x1="170" y1="260" x2="120" y2="250" />
            
            <!-- 试验到指导关系 -->
            <line class="connection-line" x1="160" y1="240" x2="200" y2="240" />
            
            <!-- 指导关系到教师（连接到上方教师） -->
            <line class="connection-line" x1="260" y1="240" x2="600" y2="120" />
            
            <!-- 第三行：教师 -> 点播课程 -> 成绩 -->
            
            <!-- 教师（下方）实体的属性连接线 -->
            <line class="connection-line" x1="280" y1="320" x2="280" y2="350" />
            <line class="connection-line" x1="240" y1="380" x2="260" y2="370" />
            <line class="connection-line" x1="320" y1="380" x2="300" y2="370" />
            <line class="connection-line" x1="280" y1="410" x2="280" y2="390" />
            <line class="connection-line" x1="340" y1="410" x2="310" y2="390" />
            
            <!-- 教师到教学关系 -->
            <line class="connection-line" x1="340" y1="360" x2="380" y2="360" />
            
            <!-- 教学关系到点播课程 -->
            <line class="connection-line" x1="440" y1="360" x2="480" y2="360" />
            
            <!-- 点播课程实体的属性连接线 -->
            <line class="connection-line" x1="520" y1="320" x2="520" y2="350" />
            <line class="connection-line" x1="480" y1="380" x2="500" y2="370" />
            <line class="connection-line" x1="560" y1="380" x2="540" y2="370" />
            
            <!-- 点播课程到属于关系 -->
            <line class="connection-line" x1="580" y1="360" x2="620" y2="360" />
            
            <!-- 属于关系到成绩 -->
            <line class="connection-line" x1="680" y1="360" x2="720" y2="360" />
            
            <!-- 成绩实体的属性连接线 -->
            <line class="connection-line" x1="760" y1="320" x2="760" y2="350" />
            <line class="connection-line" x1="720" y1="380" x2="740" y2="370" />
            <line class="connection-line" x1="780" y1="380" x2="760" y2="370" />
            <line class="connection-line" x1="820" y1="380" x2="790" y2="370" />
            <line class="connection-line" x1="760" y1="410" x2="760" y2="390" />
            <line class="connection-line" x1="820" y1="410" x2="790" y2="400" />
            
            <!-- 第四行：网络课程 -> 前端课程 -->
            
            <!-- 网络课程实体的属性连接线 -->
            <line class="connection-line" x1="280" y1="470" x2="280" y2="500" />
            <line class="connection-line" x1="240" y1="530" x2="260" y2="520" />
            <line class="connection-line" x1="320" y1="530" x2="300" y2="520" />
            
            <!-- 网络课程到属于关系 -->
            <line class="connection-line" x1="340" y1="510" x2="380" y2="510" />
            
            <!-- 属于关系到前端课程 -->
            <line class="connection-line" x1="440" y1="510" x2="480" y2="510" />
            
            <!-- 前端课程实体的属性连接线 -->
            <line class="connection-line" x1="520" y1="470" x2="520" y2="500" />
            <line class="connection-line" x1="480" y1="530" x2="500" y2="520" />
            <line class="connection-line" x1="560" y1="530" x2="540" y2="520" />
            <line class="connection-line" x1="620" y1="530" x2="570" y2="520" />
            
            <!-- 教师到教学关系的连接（从上方教师到下方教学） -->
            <line class="connection-line" x1="620" y1="120" x2="410" y2="330" />
        </svg>

        <!-- 第一行：用户 -> 课程 -> 教师 -->

        <!-- 用户实体 -->
        <div class="entity" style="left: 80px; top: 60px;">用户</div>

        <!-- 用户属性 -->
        <div class="attribute" style="left: 50px; top: 10px;">ID</div>
        <div class="attribute" style="left: 110px; top: 10px;">角色</div>
        <div class="attribute" style="left: 170px; top: 10px;">ID</div>
        <div class="attribute" style="left: 10px; top: 70px;">密码</div>
        <div class="attribute" style="left: 10px; top: 100px;">姓名</div>
        <div class="attribute" style="left: 110px; top: 100px;">性别</div>
        <div class="attribute" style="left: 170px; top: 100px;">电话</div>
        <div class="attribute" style="left: 50px; top: 130px;">手机</div>

        <!-- 选择关系 -->
        <div class="relationship" style="left: 200px; top: 70px;">选择</div>

        <!-- 课程实体 -->
        <div class="entity" style="left: 320px; top: 60px;">课程</div>

        <!-- 课程属性 -->
        <div class="attribute" style="left: 330px; top: 10px;">ID</div>
        <div class="attribute" style="left: 390px; top: 10px;">课程名</div>
        <div class="attribute" style="left: 290px; top: 100px;">学分</div>
        <div class="attribute" style="left: 350px; top: 100px;">价格</div>
        <div class="attribute" style="left: 410px; top: 100px;">课时</div>
        <div class="attribute" style="left: 330px; top: 130px;">价格</div>
        <div class="attribute" style="left: 390px; top: 130px;">日编号</div>

        <!-- 学习关系 -->
        <div class="relationship" style="left: 460px; top: 70px;">学习</div>

        <!-- 教师实体 -->
        <div class="entity" style="left: 580px; top: 60px;">教师</div>

        <!-- 教师属性 -->
        <div class="attribute" style="left: 590px; top: 10px;">ID</div>
        <div class="attribute" style="left: 650px; top: 10px;">所属单位ID</div>
        <div class="attribute" style="left: 710px; top: 10px;">所属课程ID</div>
        <div class="attribute" style="left: 550px; top: 100px;">姓名</div>
        <div class="attribute" style="left: 610px; top: 100px;">授课时间</div>
        <div class="attribute" style="left: 670px; top: 100px;">课程</div>
        <div class="attribute" style="left: 590px; top: 130px;">性别</div>
        <div class="attribute" style="left: 650px; top: 130px;">专业课程</div>

        <!-- 第二行：试验 -> 教师 -->

        <!-- 试验实体 -->
        <div class="entity" style="left: 80px; top: 210px;">试验</div>

        <!-- 试验属性 -->
        <div class="attribute" style="left: 50px; top: 180px;">ID</div>
        <div class="attribute" style="left: 110px; top: 180px;">分数</div>
        <div class="attribute" style="left: 10px; top: 240px;">试验ID</div>
        <div class="attribute" style="left: 70px; top: 240px;">开始时间</div>
        <div class="attribute" style="left: 140px; top: 240px;">所属</div>

        <!-- 指导关系 -->
        <div class="relationship" style="left: 180px; top: 220px;">指导</div>

        <!-- 第三行：教师 -> 点播课程 -> 成绩 -->

        <!-- 教师实体（下方） -->
        <div class="entity" style="left: 240px; top: 330px;">教师</div>

        <!-- 教师属性（下方） -->
        <div class="attribute" style="left: 250px; top: 300px;">用户ID</div>
        <div class="attribute" style="left: 210px; top: 360px;">高等分级</div>
        <div class="attribute" style="left: 290px; top: 360px;">教师员</div>
        <div class="attribute" style="left: 250px; top: 390px;">ID</div>
        <div class="attribute" style="left: 310px; top: 390px;">ID</div>

        <!-- 教学关系 -->
        <div class="relationship" style="left: 360px; top: 340px;">教学</div>

        <!-- 点播课程实体 -->
        <div class="entity" style="left: 480px; top: 330px;">点播课程</div>

        <!-- 点播课程属性 -->
        <div class="attribute" style="left: 490px; top: 300px;">选项ID</div>
        <div class="attribute" style="left: 450px; top: 360px;">选项</div>
        <div class="attribute" style="left: 530px; top: 360px;">清单ID</div>

        <!-- 属于关系 -->
        <div class="relationship" style="left: 600px; top: 340px;">属于</div>

        <!-- 成绩实体 -->
        <div class="entity" style="left: 720px; top: 330px;">成绩</div>

        <!-- 成绩属性 -->
        <div class="attribute" style="left: 730px; top: 300px;">ID</div>
        <div class="attribute" style="left: 690px; top: 360px;">试验分</div>
        <div class="attribute" style="left: 750px; top: 360px;">类型</div>
        <div class="attribute" style="left: 790px; top: 360px;">试验分级</div>
        <div class="attribute" style="left: 730px; top: 390px;">理由ID</div>
        <div class="attribute" style="left: 790px; top: 390px;">试验分级</div>

        <!-- 第四行：网络课程 -> 前端课程 -->

        <!-- 网络课程实体 -->
        <div class="entity" style="left: 240px; top: 480px;">网络课程</div>

        <!-- 网络课程属性 -->
        <div class="attribute" style="left: 250px; top: 450px;">ID</div>
        <div class="attribute" style="left: 210px; top: 510px;">选项</div>
        <div class="attribute" style="left: 290px; top: 510px;">特点</div>

        <!-- 属于关系（下方） -->
        <div class="relationship" style="left: 360px; top: 490px;">属于</div>

        <!-- 前端课程实体 -->
        <div class="entity" style="left: 480px; top: 480px;">前端课程</div>

        <!-- 前端课程属性 -->
        <div class="attribute" style="left: 490px; top: 450px;">ID</div>
        <div class="attribute" style="left: 450px; top: 510px;">选项</div>
        <div class="attribute" style="left: 530px; top: 510px;">选项</div>
        <div class="attribute" style="left: 590px; top: 510px;">客户解析</div>

        <!-- 基数标记 -->
        <div class="cardinality" style="left: 170px; top: 60px;">1</div>
        <div class="cardinality" style="left: 290px; top: 60px;">N</div>
        <div class="cardinality" style="left: 430px; top: 60px;">N</div>
        <div class="cardinality" style="left: 550px; top: 60px;">1</div>
        <div class="cardinality" style="left: 150px; top: 210px;">N</div>
        <div class="cardinality" style="left: 240px; top: 210px;">1</div>
        <div class="cardinality" style="left: 330px; top: 320px;">M</div>
        <div class="cardinality" style="left: 450px; top: 320px;">N</div>
        <div class="cardinality" style="left: 570px; top: 320px;">M</div>
        <div class="cardinality" style="left: 690px; top: 320px;">N</div>
        <div class="cardinality" style="left: 330px; top: 470px;">N</div>
        <div class="cardinality" style="left: 450px; top: 470px;">1</div>

    </div>
</body>
</html>
