<!--
  SmartInterview AI智能面试系统 - 功能架构图（极致美观版）
  只保留项目真实功能，采用卡片化、渐变、玻璃拟态、分区色带、icon点缀等现代设计风格。
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>SmartInterview 功能架构图</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    body {
      min-height: 100vh;
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', 'Microsoft YaHei', Arial, sans-serif;
      background: linear-gradient(135deg, #e0e7ff 0%, #f8fafc 100%);
      overflow-x: hidden;
    }
    .archi-container {
      max-width: 1400px;
      margin: 40px auto 0 auto;
      padding: 32px 16px 0 16px;
      border-radius: 32px;
      box-shadow: 0 8px 40px #0002;
      background: rgba(255,255,255,0.7);
      -webkit-backdrop-filter: blur(12px);
      backdrop-filter: blur(12px);
      position: relative;
    }
    .archi-title {
      font-size: 2.6rem;
      font-weight: 800;
      letter-spacing: 2px;
      text-align: center;
      color: #2b3674;
      margin-bottom: 18px;
      text-shadow: 0 2px 12px #b3bcf5aa;
    }
    .archi-layer-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;
      background: linear-gradient(90deg, #a5b4fc 0%, #f0f4ff 100%);
      border-radius: 18px;
      box-shadow: 0 2px 12px #a5b4fc33;
      padding: 10px 0 10px 0;
      overflow-x: auto;
    }
    .archi-layer-bar span {
      flex: 1;
      text-align: center;
      font-size: 1.1rem;
      font-weight: 600;
      color: #3b4890;
      letter-spacing: 1px;
      padding: 6px 0;
      position: relative;
    }
    .archi-layer-bar span:not(:last-child)::after {
      content: '';
      position: absolute;
      right: 0; top: 25%; bottom: 25%;
      width: 2px;
      background: linear-gradient(180deg, #a5b4fc 0%, #f0f4ff 100%);
      border-radius: 2px;
    }
    .archi-main {
      display: flex;
      flex-wrap: wrap;
      gap: 32px;
      justify-content: space-between;
      margin-bottom: 32px;
    }
    .archi-card {
      flex: 1 1 320px;
      min-width: 300px;
      max-width: 420px;
      background: rgba(255,255,255,0.55);
      border-radius: 22px;
      box-shadow: 0 6px 32px #a5b4fc33, 0 1.5px 8px #fff8;
      padding: 32px 28px 24px 28px;
      margin: 0 auto;
      position: relative;
      overflow: hidden;
      transition: transform 0.18s cubic-bezier(.4,2,.6,1), box-shadow 0.18s;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1.5px solid #e0e7ff;
      animation: cardIn 0.7s cubic-bezier(.4,2,.6,1);
    }
    .archi-card:hover {
      transform: translateY(-8px) scale(1.025);
      box-shadow: 0 12px 48px #6366f1aa, 0 2px 12px #fff8;
      border-color: #6366f1;
    }
    @keyframes cardIn {
      0% { opacity: 0; transform: translateY(40px) scale(0.98); }
      100% { opacity: 1; transform: none; }
    }
    .archi-card .card-icon {
      font-size: 2.2rem;
      color: #6366f1;
      background: linear-gradient(135deg, #a5b4fc 0%, #6366f1 100%);
      border-radius: 50%;
      padding: 14px;
      margin-bottom: 12px;
      box-shadow: 0 2px 12px #a5b4fc44;
      display: inline-block;
    }
    .archi-card .card-title {
      font-size: 1.35rem;
      font-weight: 700;
      color: #2b3674;
      margin-bottom: 10px;
      letter-spacing: 1px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .archi-card .card-list {
      margin: 0;
      padding: 0 0 0 8px;
      list-style: none;
    }
    .archi-card .card-list li {
      font-size: 1.05rem;
      color: #3b4890;
      margin-bottom: 7px;
      padding-left: 0.5em;
      position: relative;
      line-height: 1.7;
    }
    .archi-card .card-list li::before {
      content: '\f111';
      font-family: 'Font Awesome 6 Free';
      font-weight: 900;
      font-size: 0.5em;
      color: #6366f1;
      margin-right: 8px;
      position: relative;
      top: -2px;
    }
    .archi-card .card-list .sub {
      color: #6366f1;
      font-size: 0.98rem;
      margin-left: 1.2em;
      margin-bottom: 3px;
      font-weight: 500;
    }
    .archi-support-bar {
      display: flex;
      flex-direction: column;
      gap: 18px;
      position: absolute;
      right: -170px;
      top: 40px;
      z-index: 2;
      width: 140px;
      pointer-events: none;
    }
    .archi-support-bar .support-card {
      background: linear-gradient(90deg, #e0e7ff 60%, #fff 100%);
      border-radius: 12px;
      box-shadow: 0 2px 12px #a5b4fc33;
      padding: 10px 0 10px 0;
      text-align: center;
      font-size: 1rem;
      color: #6366f1;
      font-weight: 600;
      pointer-events: auto;
      margin-bottom: 6px;
      border: 1.5px solid #e0e7ff;
    }
    .archi-infra-bar {
      width: 100%;
      background: linear-gradient(90deg, #fffbe6 60%, #fff 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 18px 0 12px 0;
      border-radius: 0 0 32px 32px;
      box-shadow: 0 2px 8px #ffe08255;
      font-size: 1.1rem;
      gap: 36px;
      margin-top: 32px;
    }
    .archi-infra-bar span {
      color: #bfa100;
      font-weight: bold;
      letter-spacing: 1px;
      display: flex;
      align-items: center;
      gap: 6px;
    }
    .archi-infra-bar i {
      font-size: 1.2em;
    }
    @media (max-width: 1100px) {
      .archi-main { flex-direction: column; gap: 24px; }
      .archi-card { max-width: 100%; min-width: 220px; }
      .archi-support-bar { display: none; }
    }
    @media (max-width: 700px) {
      .archi-container { padding: 8px 2px; border-radius: 0; }
      .archi-title { font-size: 1.5rem; }
      .archi-main { gap: 12px; }
      .archi-card { padding: 14px 8px; }
      .archi-infra-bar { font-size: 0.95rem; gap: 16px; border-radius: 0 0 18px 18px; }
    }
  </style>
</head>
<body>
<div class="archi-container">
  <div class="archi-title">SmartInterview AI智能面试系统<br>功能架构图</div>
  <div class="archi-layer-bar">
    <span><i class="fa-solid fa-user-graduate"></i> 用户层</span>
    <span><i class="fa-solid fa-comments"></i> AI Agent层</span>
    <span><i class="fa-solid fa-layer-group"></i> 业务功能层</span>
    <span><i class="fa-solid fa-database"></i> 支撑服务层</span>
  </div>
  <div class="archi-main">
    <!-- AI Agent系统 -->
    <div class="archi-card">
      <div class="card-icon"><i class="fa-solid fa-robot"></i></div>
      <div class="card-title">AI Agent系统</div>
      <ul class="card-list">
        <li>面试官AI Agent <span class="sub">智能提问/追问</span></li>
        <li>简历分析AI Agent <span class="sub">简历解析/技能匹配</span></li>
        <li>技能评估AI Agent <span class="sub">技术测试/编程评估</span></li>
        <li>职业顾问AI Agent <span class="sub">职业规划/行业分析</span></li>
        <li>模拟面试AI Agent <span class="sub">真实面试模拟</span></li>
        <li>反馈分析AI Agent <span class="sub">表现分析/多维评分</span></li>
        <li>学习指导AI Agent <span class="sub">学习计划/资源推荐</span></li>
      </ul>
    </div>
    <!-- 业务功能层 -->
    <div class="archi-card">
      <div class="card-icon"><i class="fa-solid fa-layer-group"></i></div>
      <div class="card-title">核心业务功能</div>
      <ul class="card-list">
        <li>AI聊天页面</li>
        <li>面试模块</li>
        <li>技能评估</li>
        <li>学习中心</li>
        <li>用户中心</li>
        <li>成就系统</li>
      </ul>
    </div>
    <!-- 支撑服务层 -->
    <div class="archi-card">
      <div class="card-icon"><i class="fa-solid fa-server"></i></div>
      <div class="card-title">支撑服务</div>
      <ul class="card-list">
        <li>用户认证与权限管理</li>
        <li>AI Agent会话与对话管理</li>
        <li>技能评估与面试记录管理</li>
        <li>简历管理</li>
        <li>数据统计与分析</li>
      </ul>
    </div>
  </div>
  <!-- 右侧支撑能力（可选，移动端隐藏） -->
  <div class="archi-support-bar">
    <div class="support-card"><i class="fa-solid fa-database"></i> MySQL数据库</div>
    <div class="support-card"><i class="fa-solid fa-bolt"></i> Redis缓存</div>
    <div class="support-card"><i class="fa-solid fa-key"></i> Sa-Token认证</div>
    <div class="support-card"><i class="fa-solid fa-chart-line"></i> 日志与监控</div>
    <div class="support-card"><i class="fa-solid fa-envelope"></i> 消息队列</div>
  </div>
  <!-- 底部基础设施 -->
  <div class="archi-infra-bar">
    <span><i class="fa-brands fa-linux"></i> Linux</span>
    <span><i class="fa-solid fa-database"></i> MySQL</span>
    <span><i class="fa-solid fa-bolt"></i> Redis</span>
    <span><i class="fa-solid fa-key"></i> Sa-Token</span>
    <span><i class="fa-solid fa-chart-line"></i> 日志监控</span>
    <span><i class="fa-solid fa-envelope"></i> 消息队列</span>
  </div>
</div>
</body>
</html> 